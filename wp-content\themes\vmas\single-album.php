<?php 
$current_post = get_the_ID();
$args = array(
  'post_type' => 'album',
  'post_status' => 'publish', 
  'post__not_in' => array($current_post),
  'posts_per_page' => -1, 
  'order'     => 'asc',
);
$related = get_posts($args);
?>
<?php get_header(); ?>
<div class="page-banner-area h-banner" style="background-image: url(<?php the_post_thumbnail_url('large'); ?>);">
    <span class="bg-text top-50"><?php the_excerpt(); ?></span>
</div>
<div class="page-banner-title pt-5">
    <div class="text-center">
        <span class="fs-2 font-phudu">ALBUM</span>
        <h1 class="title-main py-4"><?php the_title(); ?></h1>
        <h5 class="mb-3"><?php the_excerpt(); ?></h5>
    </div>
</div>
<div class="trending py-80">
    <div class="container">
        <?php
        while (have_posts()) : the_post(); 
            $spotify_link = get_post_meta($post->ID, '_spotify_link', true);
            // $youtube_link = get_post_meta($post->ID, '_youtube_link', true);
        ?>
        <div class="projects-main mx-auto">
            <div class="content">
                <?php the_content(); ?>
                <?php if($spotify_link): ?>
                <div class="text-center mt-3">
                    <?php echo $spotify_link; ?>
                </div>
                <?php endif; ?>
            </div>
        <?php endwhile; ?> 
        </div>
    </div>
</div>
<?php if ($related) { ?>
<section class="section-post-related pb-4">
    <div class="container">
        <div class="row row-gap-4 d-flex justify-content-center">
            <div class="text-left my-3 post-related"><h2><span>OUR PROJECTS</span></h2></div>
            <?php
                foreach ($related as $post) { 
                    setup_postdata($post);
            ?>
            <!-- Single Category Card -->
            <div class="col-xl-4 col-lg-4 col-sm-6 mb-lg-4" data-aos="slide-up" data-aos-offset="300" data-aos-delay="400">
                <div class="category-card p-3">
                    <div class="thumbnail news-img">
                        <?php the_post_thumbnail('medium', ['loading' => 'lazy', 'class' => 'thumb-img w-100']); ?>
                    </div>
                    <div class="details">
                        <h3 class="fs-2 lh-sm mb-2"><?php the_title(); ?></h3>
                        <a href="<?php the_permalink(); ?>" class="hl-btn circle-btn flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <?php } wp_reset_postdata(); ?>
        </div>
    </div>
</section>
<?php } ?>
<?php get_footer(); ?>
