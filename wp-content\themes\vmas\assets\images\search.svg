<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="257" height="239" style="width: 257px; height: 239px; background: rgba(238, 238, 238, 0); shape-rendering: auto;"><path></path><g class="layer" transform="matrix(1 0 0 1 0 0) matrix(1 0 0 1 0 0)"><g class="ani"><g><g class="layer" transform="matrix(2.8467700481414795 0 0 2.846771717071533 -13.850767135620117 -22.530675888061523) matrix(1 0 0 1 0 0)"><g class="ani"><path fill="#e0e0e0" style="y: 15px; x: 10.3px; width: 79.4px; user-select: none; r: 0; perspective-origin: 0px 0px; inline-size: 79.4px; height: 51.1px; block-size: 51.1px; fill: rgb(255, 255, 255); pointer-events: all;" d="M10.3 15h79.4v51.1H10.3z"></path></g></g><g class="layer" transform="matrix(2.8467700481414795 0 0 2.846771717071533 -13.850767135620117 -22.530675888061523) matrix(1 0 0 1 0 0)"><g class="ani"><path d="M44.1 71.3l-3 11.3h-3.3c-.5 0-1 .1-1.4.4-.7.4-1.2 1.2-1.2 2.1v.1c0 1.4 1.1 2.5 2.6 2.5h24.3c1.4 0 2.6-1.1 2.6-2.5v-.1c0-.9-.5-1.7-1.2-2.1-.4-.2-.9-.4-1.4-.4h-3.5l-2.9-11.3H44.1z" fill="#a5a6a6" style="user-select: none; r: 0; perspective-origin: 0px 0px; d: path(&quot;M 44.1 71.3 L 41.1 82.6 H 37.8 C 37.3 82.6 36.8 82.7 36.4 83 C 35.7 83.4 35.2 84.2 35.2 85.1 V 85.2 C 35.2 86.6 36.3 87.7 37.8 87.7 H 62.1 C 63.5 87.7 64.7 86.6 64.7 85.2 V 85.1 C 64.7 84.2 64.2 83.4 63.5 83 C 63.1 82.8 62.6 82.6 62.1 82.6 H 58.6 L 55.7 71.3 H 44.1 Z&quot;); fill: rgb(165, 166, 166); pointer-events: all;"></path></g></g><g class="layer" transform="matrix(2.8467700481414795 0 0 2.846771717071533 -13.850767135620117 -22.530675888061523) matrix(1 0 0 1 0 0)"><g class="ani"><path d="M92.5 64.9v.2H7.5v-.2-.1 3.4c0 2 1.6 3.5 3.5 3.5h78c2 0 3.5-1.6 3.5-3.5v-3.4.1z" fill="#666" style="user-select: none; r: 0; perspective-origin: 0px 0px; d: path(&quot;M 92.5 64.9 C 92.5 65 92.5 65 92.5 65.1 H 86.4 H 13.6 H 7.5 C 7.5 65 7.5 65 7.5 64.9 C 7.5 64.9 7.5 64.8 7.5 64.8 V 68.2 C 7.5 70.2 9.1 71.7 11 71.7 H 89 C 91 71.7 92.5 70.1 92.5 68.2 V 64.8 C 92.5 64.8 92.5 64.9 92.5 64.9 Z&quot;); fill: rgb(102, 102, 102); pointer-events: all;"></path></g></g><g class="layer" transform="matrix(2.8467700481414795 0 0 2.846771717071533 -13.850767135620117 -22.530675888061523) matrix(1 0 0 1 0 0)"><g class="ani"><path d="M89 12.2H11.1c-2 0-3.5 1.6-3.5 3.5V65h85.1v-.2-49.1c-.2-1.9-1.8-3.5-3.7-3.5zm-.4 47.9v.9H11.5V16.6h77.1v43.5z" style="user-select: none; r: 0; perspective-origin: 0px 0px; d: path(&quot;M 89 12.2 H 86 H 14.1 H 11.1 C 9.1 12.2 7.6 13.8 7.6 15.7 V 18.7 V 64.7 C 7.6 64.7 7.6 64.8 7.6 64.8 C 7.6 64.9 7.6 64.9 7.6 65 H 13.7 H 86.6 H 92.7 C 92.7 64.9 92.7 64.9 92.7 64.8 C 92.7 64.8 92.7 64.7 92.7 64.7 V 18.7 V 15.7 C 92.5 13.8 90.9 12.2 89 12.2 Z M 88.6 60.1 V 61 C 88.4 61 88.2 61 88.1 61 H 12 C 11.8 61 11.6 61 11.5 61 V 60.2 V 16.6 H 88.6 V 60.1 Z&quot;); pointer-events: all;"></path></g></g><g class="layer" transform="matrix(1.8099493980407715 0 0 1.809950590133667 40.834781646728516 -2.396291732788086) matrix(1 0 0 1 0 0)"><g class="ani" style="transform-origin: 46.0375px 53.7745px; transform: matrix(1, 0, 0, 1, 0, 0); animation: 5.26316s linear 0s infinite normal forwards running swim-66534084-c0d8-401b-b62c-b3f75d590ac5;"><path d="M58.941 36.021c-3.936-6.817-12.684-9.161-19.501-5.225s-9.161 12.684-5.226 19.501c3.709 6.424 11.69 8.874 18.303 5.842l1.583 2.742a3.229 3.229 0 0 0-.165 3.484l5.141 8.905a3.232 3.232 0 0 0 5.597-3.231l-5.141-8.905a3.23 3.23 0 0 0-3.1-1.599l-1.583-2.742c5.932-4.211 7.801-12.348 4.092-18.772z" fill="#77a4bd" style="user-select: none; r: 0; perspective-origin: 0px 0px; d: path(&quot;M 58.941 36.021 C 55.005 29.204 46.257 26.86 39.44 30.796 S 30.279 43.48 34.214 50.297 C 37.923 56.721 45.904 59.171 52.517 56.139 L 54.1 58.881 C 53.393 59.879 53.283 61.236 53.935 62.365 L 59.076 71.27 C 59.968 72.815 61.945 73.345 63.49 72.453 C 65.035 71.561 65.565 69.584 64.673 68.039 L 59.532 59.134 C 58.88 58.005 57.65 57.422 56.432 57.535 L 54.849 54.793 C 60.781 50.582 62.65 42.445 58.941 36.021 Z&quot;); fill: rgb(119, 164, 189); pointer-events: all;"></path></g></g></g></g></g><style id="swim-7eebac1e-8ba6-4a5f-b9e6-2ca0490dd622" data-anikit="">@keyframes swim-7eebac1e-8ba6-4a5f-b9e6-2ca0490dd622 {
  0% { transform: translate(0px,0px) rotate(0deg) scale(1) }
  8.33333% { transform: translate(-7.455770563038781px,6.135563913969156px) rotate(-13.668208599899142deg) scale(1); animation-timing-function: linear }
  16.66667% { transform: translate(6.5683879938227125px,-2.5700689713541927px) rotate(0.47337246955805057deg) scale(1); animation-timing-function: linear }
  25% { transform: translate(2.8866182291025275px,7.24215370082965px) rotate(13.856398766957234deg) scale(1); animation-timing-function: linear }
  33.33333% { transform: translate(-7.194451799864137px,-5.760019531787629px) rotate(10.942457102398254deg) scale(1); animation-timing-function: linear }
  41.66667% { transform: translate(4.669019830829722px,7.471772718635288px) rotate(-14.030785412713554deg) scale(1); animation-timing-function: linear }
  50% { transform: translate(5.328498701232251px,-3.76528096299853px) rotate(-10.613227721848103deg) scale(1); animation-timing-function: linear }
  58.33333% { transform: translate(-5.824692750387111px,-3.140863407626389px) rotate(5.286897990634351deg) scale(1); animation-timing-function: linear }
  66.66667% { transform: translate(2.0503062003775554px,-1.314591821306407px) rotate(10.273425806369517deg) scale(1); animation-timing-function: linear }
  75% { transform: translate(6.949423864299401px,-5.284540438072463px) rotate(-5.727258555619329deg) scale(1); animation-timing-function: linear }
  83.33333% { transform: translate(-3.5575256561309394px,7.392991270224293px) rotate(-14.991925396002646deg) scale(1); animation-timing-function: linear }
  91.66667% { transform: translate(-0.8842984095024526px,-6.788300342855515px) rotate(6.161915473106543deg) scale(1); animation-timing-function: linear }
  100% { transform: translate(0,0) rotate(0) scale(1) }
}</style><style id="swim-66534084-c0d8-401b-b62c-b3f75d590ac5" data-anikit="">@keyframes swim-66534084-c0d8-401b-b62c-b3f75d590ac5 {
  0% { transform: translate(0px,0px) rotate(0deg) scale(1) }
  8.33333% { transform: translate(-7.455770563038781px,6.135563913969156px) rotate(-13.668208599899142deg) scale(1); animation-timing-function: linear }
  16.66667% { transform: translate(6.5683879938227125px,-2.5700689713541927px) rotate(0.47337246955805057deg) scale(1); animation-timing-function: linear }
  25% { transform: translate(2.8866182291025275px,7.24215370082965px) rotate(13.856398766957234deg) scale(1); animation-timing-function: linear }
  33.33333% { transform: translate(-7.194451799864137px,-5.760019531787629px) rotate(10.942457102398254deg) scale(1); animation-timing-function: linear }
  41.66667% { transform: translate(4.669019830829722px,7.471772718635288px) rotate(-14.030785412713554deg) scale(1); animation-timing-function: linear }
  50% { transform: translate(5.328498701232251px,-3.76528096299853px) rotate(-10.613227721848103deg) scale(1); animation-timing-function: linear }
  58.33333% { transform: translate(-5.824692750387111px,-3.140863407626389px) rotate(5.286897990634351deg) scale(1); animation-timing-function: linear }
  66.66667% { transform: translate(2.0503062003775554px,-1.314591821306407px) rotate(10.273425806369517deg) scale(1); animation-timing-function: linear }
  75% { transform: translate(6.949423864299401px,-5.284540438072463px) rotate(-5.727258555619329deg) scale(1); animation-timing-function: linear }
  83.33333% { transform: translate(-3.5575256561309394px,7.392991270224293px) rotate(-14.991925396002646deg) scale(1); animation-timing-function: linear }
  91.66667% { transform: translate(-0.8842984095024526px,-6.788300342855515px) rotate(6.161915473106543deg) scale(1); animation-timing-function: linear }
  100% { transform: translate(0,0) rotate(0) scale(1) }
}</style><style id="swim-7eebac1e-8ba6-4a5f-b9e6-2ca0490dd622" data-anikit="">@keyframes swim-7eebac1e-8ba6-4a5f-b9e6-2ca0490dd622 {
  0% { transform: translate(0px,0px) rotate(0deg) scale(1) }
  8.33333% { transform: translate(-7.455770563038781px,6.135563913969156px) rotate(-13.668208599899142deg) scale(1); animation-timing-function: linear }
  16.66667% { transform: translate(6.5683879938227125px,-2.5700689713541927px) rotate(0.47337246955805057deg) scale(1); animation-timing-function: linear }
  25% { transform: translate(2.8866182291025275px,7.24215370082965px) rotate(13.856398766957234deg) scale(1); animation-timing-function: linear }
  33.33333% { transform: translate(-7.194451799864137px,-5.760019531787629px) rotate(10.942457102398254deg) scale(1); animation-timing-function: linear }
  41.66667% { transform: translate(4.669019830829722px,7.471772718635288px) rotate(-14.030785412713554deg) scale(1); animation-timing-function: linear }
  50% { transform: translate(5.328498701232251px,-3.76528096299853px) rotate(-10.613227721848103deg) scale(1); animation-timing-function: linear }
  58.33333% { transform: translate(-5.824692750387111px,-3.140863407626389px) rotate(5.286897990634351deg) scale(1); animation-timing-function: linear }
  66.66667% { transform: translate(2.0503062003775554px,-1.314591821306407px) rotate(10.273425806369517deg) scale(1); animation-timing-function: linear }
  75% { transform: translate(6.949423864299401px,-5.284540438072463px) rotate(-5.727258555619329deg) scale(1); animation-timing-function: linear }
  83.33333% { transform: translate(-3.5575256561309394px,7.392991270224293px) rotate(-14.991925396002646deg) scale(1); animation-timing-function: linear }
  91.66667% { transform: translate(-0.8842984095024526px,-6.788300342855515px) rotate(6.161915473106543deg) scale(1); animation-timing-function: linear }
  100% { transform: translate(0,0) rotate(0) scale(1) }
}</style></svg>