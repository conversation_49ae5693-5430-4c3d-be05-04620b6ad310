<?php
$args = array(
    'post_type' => 'artist',
    'orderby'   => 'menu_order',
    'order'     => 'asc',
);

$query = new WP_Query($args);
?>

<section class="section-artist pt-80">
    <div class="container">
        <div class="d-flex align-items-center justify-content-center">
            <div class="d-inline-flex align-item-center section-header">
               <h1 class="title-main pb-80">NGHỆ SĨ</h1>
            </div>
        </div>
    </div>
    <!-- Sliders -->
    <div class="swiper movie-card-slider-sm-scroll mb-5">
        <div class="swiper-wrapper">
            <!-- Single Trending Movie Card -->
            <?php
            if ($query->have_posts()) :
                while ($query->have_posts()) : $query->the_post();
            ?>
            <div class="movie-card-small position-relative swiper-slide">
                <div class="thumb">
                    <?php the_post_thumbnail('medium_large', ['loading' => 'lazy','class' => 'w-100']); ?>
                </div>
                <a href="<?php the_permalink(); ?>" class="video-play-btn position-absolute">
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor"/>
                    </svg>
                </a>
                <div class="details position-absolute text-center">
                    <h4 class="font-phudu"><a href="<?php the_permalink(); ?>" class="gradient-link fw-bold"><?php the_title(); ?></a></h4>
                </div>
            </div>
            <?php endwhile;
            // Reset post data
            wp_reset_postdata();
            else :
                echo '<p>No artists found.</p>';
            endif;
            ?>
        </div>
    </div>
</section>
