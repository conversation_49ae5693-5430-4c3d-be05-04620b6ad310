/*
Theme Name: VMAS Theme
Theme URI: https://vmas.vn
Author: VMAS
Author URI: https://vmas.vn
Description: A custom WordPress theme for VMAS.
Version: 1.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: vmas-theme
*/
@import url('https://fonts.googleapis.com/css2?family=Phudu:wght@300..900&display=swap');
html{
    font-size: 62.5%;	
}

body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.home-one-slider img {
    aspect-ratio: 16/7;
    object-fit: cover;
}

.font-phudu {
    font-family: "Phudu", sans-serif !important;
    font-style: normal;
}

.font-poppin {
    font-family: "Poppins", sans-serif !important;
    font-style: normal;
}

.main-body {
    max-width: 100%;
}

.title-main {
    font-size: 42px;
    font-weight: 600;
    font-family: var(--heading-font);
    color: var(--heading-color);
    line-height: 1.3;
    letter-spacing: 3px;
}

.aritst .title-main {
    letter-spacing: 5px;
}

section.music {
    background: linear-gradient(0deg, #9b1316, #101010)
}

.bg-page {
    background: linear-gradient(180deg, #5e0002, #101010)
}

.navbar-area .nav-container {
    transition: all 0.4s;
    z-index: 999;
    background-color: var(--primary-color);
    padding: 10px 0;
    width: 100%;
    position: relative;
}

.fade_down_effect {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.sticky {
    position: fixed !important;
    z-index: 99;
    padding: 10px 0;
}

/* .hero-slider-one {
    min-height: 450px;
    max-height: 100vh;
} */

.top-40 {
    top: 40% !important;
}

.banner-title h1 {
    font-weight: bold;
    font-size: 100px;
    color: #fff;
    line-height: 1.2;
    margin-bottom: 20px;
}

.banner-title h2 {
    font-weight: bold !important;
    font-size: 50px;
    color: #fff;
    font-weight: 400;
    margin-bottom: 15px;
}

.social-media--link {
    background: #ffffff;
    color: #000000;
}

ul.menu-address li {
    text-align: right;
}

a:hover,
a:focus {
    outline: none;
    text-decoration: none;
}

.main-post {
    background-color: #fff;
}

.content-main {
    margin: 0 auto;
    max-width: 900px;
}

.content-main h1.title {
    font-family: var(--body-font);
    color: #222222;
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: left;
    font-size: 38px;
    line-height: 1.15;
    letter-spacing: normal;
    font-weight: 600;
}

.content-main .wp-block-heading {
    font-family: var(--body-font);
    color: #222222;
    font-size: 19px;
    line-height: 1.67;
    margin-bottom: 40px;
    font-weight: 600;
}

.content-main .wp-block-image {
    margin: 20px auto;
    text-align: center;
    max-width: 100%;
}

.content-main .wp-element-caption {
    font-family: var(--body-font);
    font-weight: 300;
    font-size: 14px;
    line-height: 1.56;
    color: #222222;
    opacity: 0.8;
    padding-top: 8px;
}

.content-main p {
    font-family: var(--body-font);
    font-style: normal;
    font-size: 17px;
    line-height: 1.66;
    color: #222222;
    margin: 0 auto 24px;
}

.details .title {
    font-family: var(--body-font);
    text-transform: unset !important;
    font-weight: 600;
    font-size: 18px;
    line-height: 1.66;
}

.section-post-related {
    background-color: #101010;
}

.title-teams {
    font-size: 50px;
}

.post-related h2 {
    font-size: 34px;
    position: relative;
    display: inline;
}

.post-related h2::before {
    content: '';
    position: absolute;
    width: 6px;
    left: 0;
    top: 12px;
    bottom: 5px;
    -webkit-transform: skew(-20deg);
    -khtml-transform: skew(-20deg);
    -moz-transform: skew(-20deg);
    -o-transform: skew(-20deg);
    transform: skew(-20deg);
    background-color: #dd3333;
}

.post-related h2 span {
    margin-left: 16px;
    font-family: var(--heading-font);
    font-weight: 400;
    font-size: 32px;
    line-height: 1.66;
    color: #f2f2f2;
}

.projects-main {
    max-width: 900px;
    margin: 0 auto;
}

.projects-main .content {
    font-size: 17px;
    text-align: justify;
    margin-bottom: 20px;
}

#myTab {
    justify-content: center;
    margin-bottom: 40px;
    border: 0;
}

#myTabContent {
    max-width: 900px;
}

.nav-tabs .nav-link {
    width: 100%;
    color: #fff;
    background: unset;
    padding: 15px;
    border: 0;
    font-family: var(--heading-font);
    font-weight: 400;
    font-size: 22px;
    letter-spacing: 3px;
}

.nav-tabs .nav-link.active {
    background-color: transparent !important;
    color: var(--secondary-color) !important;
    border-bottom: 2px solid !important;
    border-radius: 0;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border: 0;
    border-radius: 0;
    color: #fff;
    border-color: unset;
    margin-bottom: 0;
    padding: 15px;
}

.nav-tabs .nav-link {
    transition: none !important;
    margin-bottom: 0 !important;
    border: 0 !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
}

.nav-link:focus,
.nav-link:hover {
    color: #fff;
}

.popup_mobile_menu ul li {
    color: #b50004;
    text-transform: uppercase;
    font-family: var(--heading-font);
    font-size: 30px;
    font-weight: 600;
    line-height: 1.6;
}

.popup_mobile_menu .sub-menu li {
    color: var(--heading-color);
    text-transform: uppercase;
    font-family: var(--heading-font);
    font-size: 24px;
    font-weight: 400;
}

.section-projects,
.section-music,
.trending,
.rotate-movie-slider {
    background-color: var(--body-bg);
}

.page-banner-title span {
    font-weight: 400;
}

.aritst .main-body{
    margin: 0 auto;
    max-width: 800px;
    width: 100%;
}

.aritst .main-body p {
    font-size: 16px !important;
    font-family: var(--body-font);
    line-height: 1.6;
    font-weight: 300;
    margin-bottom: 15px;
}

.aritst .main-body h1,
.aritst .main-body h2 {
    font-family: var(--heading-font);
    text-transform: uppercase;
    font-weight: 500;
    font-size: 32px;
    line-height: 1.66;
}

.aritst .main-body ul,
.aritst .main-body ol {
    list-style: auto;
    margin-left: 20px;
}

.aritst .main-body li {
    padding: 15px 0;
    font-size: 16px;
    text-transform: uppercase;
    border-bottom: 1px dotted #333333;
    line-height: 1.66;
}

.h-banner {
    height: 60vh;
	height: 60vh;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    opacity: .7;
} 

.page-banner-area {
    position: relative;
    z-index: 1;
}

.page-banner-area:after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background-image: linear-gradient(180deg, #4100016e, #000000);
    opacity: .9;
    content: "";
}

.page-banner-area span.bg-text {
    font-size: 6vw;
    font-family: Arial, sans-serif;
    letter-spacing: 10px;
    font-weight: 700;
    text-transform: uppercase;
    line-height: 1.2;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 80%;
    transform: translate(-50%, -50%);
    color: transparent;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke: 1px #fff;
    z-index: -1;
    opacity: .2;
    width: 100%;
}

.BackTo {
    background: #aa0404 none repeat scroll 0 0;
    border-radius: 50%;
    bottom: 35px;
    color: #dadada;
    cursor: pointer;
    height: 44px;
    position: fixed;
    right: 14px;
    text-align: center;
    width: 44px;
    z-index: 999;
    display: block;
    padding: 8px 0;
}

.BackTo a {
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    margin-top: 2px;
}

#about-us {
    background-color: var(--primary-color);
}

#about-us p {
    font-size: 16px;
    text-align: justify;
    text-transform: uppercase;
    color: #f2f2f2;
    font-family: var(--heading-font);
}

.page-banner-title h2 {
    font-size: 38px;
    font-weight: 600;
}

.page-banner-title h3 {
    font-size: 40px;
}

#about-us figure {
    margin: 0;
}

.main-popup {
    background: linear-gradient(0deg, var(--body-bg), #101010);
    padding: 0;
    position: relative;
}

.scroll-y p {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
}

#about-us h1{
    font-weight: bold;
    line-height: 1.1;
    letter-spacing: 2px;
}

#about-us h3{
    font-size: 2rem;
}



.main-popup.brand {
    margin: 0 auto;
    width: 100%;
    max-width: 800px !important;
    background: none;
    height: 600px;
}

.main-popup.brand iframe {
    width: 100% !important;
}

.main-popup.brand p {
    padding: 0;
    margin: 0;
}

.card-brand img {
    display: flex;
    aspect-ratio: 1 / 1;
    object-fit: contain;
    object-position: center;
    padding: 10px;
}
.category-list--item .thumb img {
    transition: transform 300ms ease-out;
}

.category-list--item .thumb {
    border-radius: 5px;
    overflow: hidden;
}

.category-list--item .thumb:hover img {
    transform: scale(1.1);
    
}

.rotate-movie-card .thumb img,
.category-list--item .thumb img,
.section-projects .thumb img,
.section-post-related .thumb img,
.teams .thumb img,
.news-img img {
    display: flex;
    aspect-ratio: 1/1;
    object-fit: cover;
    object-position: top;
    height: auto;
}

.news-img-main img{
    display: flex;
    aspect-ratio: 4/3;
    object-fit: cover;
    object-position: center;
    height: auto;
}

.team .card-body{
    padding: 15px 0;
}

.team .card-body h3 {
    font-family: var(--heading-font);
    margin: 0;
    padding: 0;
    font-weight: 400;
    font-size: 17px !important;
}

.team .card-body p {
    font-family: var(--heading-font);
    text-transform: uppercase !important;
    font-weight: 300;
    font-size: 14px !important;
    margin-top: 7px;
}

.main-body .team img{
    display: flex;
    aspect-ratio: 3 / 4;
    object-fit: cover;
    object-position: top;
    height: auto;
}

.gradient-link {
    background-image: linear-gradient(92deg, #9b0000 0%, #ff1919 20.6%, #dc3545 45%, #fff 55%) !important;
}

.category-card .details h3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.category-card .details {
    gap: 20px !important;
}

.movie-card-small,
.movie-card-small .details {
    border-radius: 15px !important;
}

.card-brand {
    opacity: .7;
    transition: opacity 0.6s ease;
}

.card-brand:hover {
    opacity: 1;
}

.card {
    transition: opacity 0.6s ease;
}

.is-hover:hover .card> :not(:hover) {
    opacity: 0.6;
}

#about-us .wp-block-image img {
    width: 100%;
}

.main-single p {
    font-size: 20px;
    color: #c5c5c5;
}

.footer-widget--list,
.copyright {
    font-size: 13px;
}

.category-list .category-list--item {
    align-items: center;
    padding: 30px 0;
    border-bottom: 1px solid #201f1f;
}
.category-list--item .title{
    font-size: 38px;
    font-weight: 500;
    line-height: 1.2;
}

.category-list--item .excerpt{
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

.category-list--item .excerpt p{
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.main-popup{
    max-width: 750px;
    margin: 0 auto;
}

#profile .content iframe{
    margin-bottom: 20px;
}

.bg-size{
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

@media only screen and (max-width: 991px) {
    #about-us h1{
        font-size: 2rem;
    }
    .category-list--item .title{
        font-size: 32px;
    }
    .category-list--item .thumb img {
        width: 100%;
    }
    .home-one-slider .banner-title h1 {
        font-size: 65px;
    }

    .home-one-slider .banner-title h2 {
        font-size: 40px;
    }

    .home-one-slider img {
        aspect-ratio: 4/3;
	object-fit: cover;
    }

    #about-us .wp-block-heading {
        font-size: 45px;
        margin-top: 30px;
        text-align: center;
    }

    .is-hover:hover .card> :not(:hover) {
        opacity: 1;
    }

    .section-header img {
        width: 100px;
    }

    .title-teams {
        font-size: 38px;
    }

    .logo-footer img {
        max-width: 190px;
        margin-bottom: 30px;
    }

    .footer-area .footer-bottom {
        padding: 30px 0;
        margin-top: 20px;
    }

    .content-main h1.title {
        font-size: 40px;
        line-height: 1.2;
    }

    ul.menu-address li {
        text-align: left;
    }

    .h-banner {
        height: 40vh;
    }
}

@media only screen and (max-width: 767.98px) {
    .home-one-slider .banner-title h1 {
        font-size: 38px;
    }
    .home-one-slider .banner-title h2 {
        font-size: 34px;
    }
    #about-us h1{
        font-size: 1.8rem;
    }

    #about-us h3{
        font-size: 1rem;
    }

    .category-list--item .title{
        font-size: 28px;
    }
    
    .page-banner-title h2 {
        font-size: 32px;
    }

    .content-main h1.title {
        margin-top: 0;
        font-size: 32px;
        line-height: 1.2;
    }

    .content-main p {
        font-size: 16px;
        line-height: 1.6;
    }

    .navbar-area .nav-container {
        padding: 5px 0;
        background-color: var(--primary-color);
    }

    .post-related {
        margin-bottom: 20px !important;
    }

    .home-one-slider .banner-title {
        margin-top: 40px;
    }

    .home-one-slider .banner-title h1 {
        font-size: 50px;
    }

    .main-popup .p-5 {
        padding: 15px !important;
    }

    .album .thumb,
    .main-popup .thumb {
        display: none;
    }

    .main-body h1 {
        font-size: 28px;
        text-align: center;
    }

    .rotate-movie-card .card-description {
        padding: 0 10px !important;
        font-size: 15px !important;
        display: block !important;
    }

    .rotate-movie-card .card-title {
        font-size: 22px !important;
        margin-bottom: 10px !important;
    }
    .movie-card-small .details h4{
        font-size: 22px !important;
    }

    .title-main {
        font-size: 32px;
        font-weight: 500;
        line-height: 1.2;
    }

    .aritst .title-main {
        font-size: 36px;
        letter-spacing: 2px;
    }

    .title-project {
        font-size: 26px;
        line-height: 1.4;
        letter-spacing: 1px;
    }

    .popup_mobile_menu ul li {
        font-size: 20px;
    }

    .popup_mobile_menu .sub-menu li {
        font-size: 16px;
    }

    .popup_mobile_menu .sub-menu li.address {
        font-size: 16px !important;
    }

    .popup_mobile_menu .menu__top .menu_header {
        padding-bottom: 30px;
    }

    .nav-tabs .nav-link {
        font-size: 17px;
        letter-spacing: normal;
        padding: 10px;
    }

    .nav-tabs .nav-link:focus,
    .nav-tabs .nav-link:hover {
        padding: 10px;
    }

    .page-banner-area {
        background-size: contain;
        background-position: top;
    }

    .page-banner-area span.bg-text {
        font-size: 10vw;
    }
}

#google_language_translator,
.skiptranslate {
    display: none;
}

.mfp-close-btn-in .mfp-close {
    color: #fff !important;
    background: var(--secondary-color);
}

.movie-card-small .thumb img {
    width: 100% !important;
    height: 100% !important;
}

.movie-card-small .thumb img,
.main-popup .thumb img {
    display: flex;
    aspect-ratio: 1/1;
    object-fit: cover;
}

.brand .movie-card-small .thumb img{
    aspect-ratio: 3/2;
}


.animate-bottom {
    position: relative;
    -webkit-animation-name: animatebottom;
    -webkit-animation-duration: 1s;
    animation-name: animatebottom;
    animation-duration: 1s
}

@-webkit-keyframes animatebottom {
    from {
        bottom: -100px;
        opacity: 0
    }

    to {
        bottom: 0px;
        opacity: 1
    }
}

@keyframes animatebottom {
    from {
        bottom: -100px;
        opacity: 0
    }

    to {
        bottom: 0;
        opacity: 1
    }
}

.btn-base {
    background-image: unset !important;
    border: 1px solid #f2f2f2;
}

.btn-base:after {
    background: unset !important;
}

::-webkit-scrollbar {
    width: 4px;
    background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
    background-color: #ff272e;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #F5F5F5;
}


/* Đảm bảo các hình ảnh trong gallery chiếm toàn bộ chiều rộng của container trên các thiết bị nhỏ */
@media (max-width: 768px) {
    .gallery-item {
        width: 50% !important;
        /* Điều chỉnh tỷ lệ này nếu cần */
        max-width: 50% !important;
    }

    .pb-80 {
        padding-bottom: 40px !important;
    }
}

@media (max-width: 480px) {
    .gallery-item {
        width: 100% !important;
        /* Điều chỉnh tỷ lệ này nếu cần */
        max-width: 100% !important;
    }
}

/* Điều chỉnh khoảng cách giữa các hình ảnh trong gallery */
.gallery {
    margin-bottom: 20px;
}

.gallery-item {
    margin-bottom: 10px;
    /* Bạn có thể thay đổi giá trị này */
}

/* Điều chỉnh kiểu hiển thị của hình ảnh */
.gallery-item img {
    width: 100%;
    height: auto;
    aspect-ratio: 1 / 1;
    object-fit: contain;
    border: 0 !important;
}

.icon-bar {
    position: absolute;
    z-index: 3;
    top: 50%;
    right: 14px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.icon-bar li {
    display: block;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}
