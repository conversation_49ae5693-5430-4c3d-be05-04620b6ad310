<?php
/*
Template Name: Trang Dự án
*/
get_header(); ?>

<?php
$term = get_term_by('slug', 'du-an', 'project_category');
$args = array(
    'post_type' => 'project',
    'orderby'   => 'menu_order',
    'order'     => 'asc',
    'tax_query' => array(
        array(
        'taxonomy' => 'project_category',
        'field' => 'term_id',
        'terms' => $term->term_id
        )
    )
);

$query = new WP_Query($args);
?>
<div class="page-banner-area"></div>
<div class="page-banner-title py-5 bg-page">
    <div class="text-center mt-5">
        <h1 class="title-main"><?php _e($term->name,'vms-thems'); ?></h1>
        <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
    </div>
</div>
<main class="main mt-5">
    <section class="section-projects mb-80">
        <div class="container py-3">
            <div class="row row-gap-4 d-flex justify-content-center">
            <?php
            if ($query->have_posts()) {
                $k=1;
                while ($query->have_posts()) {
                $query->the_post(); 
            ?>
            <!-- Single Category Card -->
            <div class="col-xl-4 col-lg-4 col-sm-6 mb-lg-4">
                <div class="category-card p-3" data-aos="fade-up" data-aos-offset="300" data-aos-delay="<?php echo $k; ?>00ms">
                    <div class="thumbnail news-img">
                        <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'w-100']); ?>               
                    </div>
                    <div class="details">
                        <h3 class="fs-2 lh-sm mb-2"><?php the_title(); ?></h3>
                        <a href="<?php the_permalink(); ?>" class="hl-btn circle-btn flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <?php $k++; } } wp_reset_postdata(); ?>
            </div>
        </div>
    </section>
<?php

$term = get_term_by('slug', 'hop-tac', 'project_category');
$args = array(
    'post_type' => 'project',
    'orderby'   => 'menu_order',
    'order'     => 'asc',
    'tax_query' => array(
        array(
        'taxonomy' => 'project_category',
        'field' => 'term_id',
        'terms' => $term->term_id
        )
    )
);
$partner = new WP_Query($args);
if ($partner->have_posts()):
?>
    <section class="section-projects">
        <div class="page-banner-title py-5 bg-page">
            <div class="text-center">
                <h1 class="title-main"><?php _e($term->name,'vms-thems'); ?></h1>
                <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
            </div>
        </div>
        <div class="container py-3">
            <div class="row row-gap-4 d-flex justify-content-center">
            <?php
                $k=1;
                while ($partner->have_posts()) : 
                $partner->the_post(); 
            ?>
            <!-- Single Category Card -->
            <div class="col-xl-4 col-lg-4 col-sm-6 mb-lg-4">
                <div class="category-card p-3" data-aos="fade-up" data-aos-offset="200">
                    <div class="thumbnail news-img">
                        <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'w-100']); ?>               
                    </div>
                    <div class="details">
                        <h3 class="fs-2 lh-sm mb-2"><?php the_title(); ?></h3>
                    </div>
                </div>
            </div>
            <?php $k++; endwhile; wp_reset_postdata(); ?>
            </div>
        </div>
    </section>
<?php endif; ?>
</main>
<?php
get_footer();
?>
