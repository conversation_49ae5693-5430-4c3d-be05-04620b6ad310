<?php
$current_post = get_the_ID();
$args = array(
    'post_type' => 'post',
    'post_status' => 'publish',
    'post__not_in' => array($current_post),
    'posts_per_page' => 12,
    'order'     => 'desc',
);
$related_posts = get_posts($args);
?>
<?php
get_header();
?>
<div class="page-banner-area">
    <div class="page-banner-title py-5 bg-page">
        <div class="text-center">
            <h2 class="font-phudu"><?php _e('TIN TỨC', 'vms-theme') ?></h2>
            <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
        </div>
    </div>
</div>
<div class="main-post">
    <div class="container py-3">
        <?php while (have_posts()) : the_post(); ?>
            <div class="justify-content-center">
                <div class="content-main">
                    <h1 class="title"><?php the_title(); ?></h1>
                    <?php the_content(); ?>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
</div>
<?php if ($related_posts) { ?>
    <section class="section-post-related">
        <div class="container py-80">
            <div class="row row-gap-4 d-flex justify-content-center">
                <div class="text-center post-related mb-4">
                    <h2><span>TIN NỔI BẬT</span></h2>
                </div>
                <?php foreach ($related_posts as $post) {
                    setup_postdata($post);
                ?>
                    <div class="col-xl-3 col-lg-4 col-sm-6" data-aos="slide-up" data-aos-offset="300" data-aos-delay="400">
                        <div class="category-card p-3">
                            <div class="thumbnail news-img">
                                <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'thumb-img w-100']); ?>
                            </div>
                            <div class="details">
                                <h3 class="fs-3 lh-sm mb-2"><?php the_title(); ?></h3>
                                <a href="<?php the_permalink(); ?>" class="hl-btn circle-btn flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php }
                wp_reset_postdata(); ?>
            </div>
        </div>
    </section>
<?php } ?>
<?php get_footer(); ?>
