<style>

#loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #131313;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.load-logo {
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0;
    bottom: 1px;
}

.logo-load{
  position: absolute;
  transform: scale(1);
  animation: moveToCorner 2s forwards;
}

.load-logo:before , .load-logo:after{
  content: '';
  border-radius: 50%;
  position: absolute;
  inset: 0;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.3) inset;
}
.load-logo:after {
  box-shadow: 0 2px 0 #FF3D00 inset;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% {  transform: rotate(0)}
  100% { transform: rotate(360deg)}
}

@keyframes moveToCorner {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  60% {
    opacity: .5;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(10);
  }
}
</style>
<div id="loading">
  <div class="logo-load">
    <div class="load-logo"></div>
    <img width="200px" class="mt-1 mt-lg-2" src="<?php echo get_template_directory_uri() . '/assets/images/loading.png' ?>" alt="logo">
  </div>
</div>