<?php

get_header(); ?>

<?php
global $query_string;
wp_parse_str( $query_string, $search_query );
$query = new WP_Query( $search_query );
?>

<div class="page-banner-area"></div>
<div class="page-banner-title py-5">
    <div class="text-center">
        <h2 class="font-phudu"><?php _e('Search', 'vms-theme')?></h2>
        <h5 class="font-phudu fs-4">Keyword: <?php echo $search_query['s']; ?></h5>
        <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
    </div>
</div>
<div class="trending">
    <div class="container">
        <div class="category-two">
            <div class="row row-gap-4 d-flex justify-content-center">
                <?php
                if ($query->have_posts()) :
                    while ($query->have_posts()) : $query->the_post();
                ?>
                <div class="col-xl-3 col-lg-4 col-sm-6" data-aos="slide-up" data-aos-offset="300" data-aos-delay="400">
                    <div class="category-card p-3">
                        <div class="thumbnail news-img">
                            <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'card-img w-100']); ?>                           
                        </div>
                        <div class="details">
                            <h3 class="fs-3 lh-sm mb-0"><?php the_title(); ?></h3>
                            <a href="<?php the_permalink(); ?>" class="hl-btn circle-btn flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <?php endwhile; endif; wp_reset_postdata(); ?>
            </div>
        </div>
    </div>
</div>
<!-- End Tranding Area -->
<?php
get_footer();
?>
