<?php
// Tối ưu hóa việc enqueue styles và scripts
function vmas_enqueue_assets() {
    $version = wp_get_theme()->get('Version');
    
    $styles = [
        'bootstrap' => 'bootstrap.min.css',
        'aos' => 'aos.css',
        'magnific' => 'magnific.css',
        'lightbox' => 'lightbox.css',
        'swiper' => 'swiper-bundle.min.css',
        // Đổi từ 'main' thành 'template' để tránh conflict
        'template' => 'style.css',
    ];

    $scripts = [
        'jquery' => 'jquery.min.js',
        'aos' => 'aos.js',
        'bootstrap' => 'bootstrap.min.js',
        'fontawesome' => 'fontawesome.min.js',
        'magnefic' => 'magnefic.min.js',
        'swiper' => 'swiper-bundle.min.js',
        'lightbox' => 'lightbox.js',
        'iframe' => 'iframeResizer.contentWindow.min.js',
        'main' => 'main.js',
    ];

    foreach ($styles as $handle => $file) {
        wp_enqueue_style($handle . '-style', get_template_directory_uri() . '/assets/template/css/' . $file, [], $version);
    }

    // Enqueue theme style cuối cùng để override
    wp_enqueue_style('vmas-theme-style', get_stylesheet_uri(), [], '1.0');

    foreach ($scripts as $handle => $file) {
        wp_enqueue_script($handle . '-script', get_template_directory_uri() . '/assets/template/js/' . $file, ['jquery'], $version, true);
    }
}
add_action('wp_enqueue_scripts', 'vmas_enqueue_assets');

// Tối ưu hóa việc đăng ký menu, post types, và taxonomies
function vmas_theme_setup() {
    // Đăng ký menus
    $menus = [
        'primary' => __('Primary Menu', 'vmas-theme'),
        'left' => __('Left Menu', 'vmas-theme'),
        'right' => __('Right Menu', 'vmas-theme'),
    ];
    foreach ($menus as $location => $description) {
        register_nav_menu($location, $description);
    }

    // Thêm hỗ trợ
    add_theme_support('post-thumbnails');
    load_theme_textdomain('vmas-theme', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'vmas_theme_setup');

// Tối ưu hóa việc đăng ký custom post types và taxonomies
function vmas_register_custom_structures() {
    $post_types = [
        'artist' => ['vi' => 'nghe-si', 'label' => 'Nghệ sĩ' , 'default' => 'artist', 'icon' => 'dashicons-format-audio'],
        'project' => ['vi' => 'du-an', 'label' => 'Dự án', 'default' => 'project', 'icon' => 'dashicons-excerpt-view'],
        'brand' => ['vi' => 'nhan-hang','label' => 'Nhãn hàng', 'default' => 'brand', 'icon' => 'dashicons-images-alt2'],
        'about' => ['vi' => 've-chung-toi','label' => 'Về chúng tôi', 'default' => 'about', 'icon' => 'dashicons-book'],
        'banner' => ['default' => 'banner', 'label' => 'Banner', 'icon' => 'dashicons-format-image'],
        'team' => ['default' => 'team', 'label' => 'Team', 'icon' => 'dashicons-businessperson'],
        'album' => ['default' => 'album', 'label' => 'Album', 'icon' => 'dashicons-format-video'],
    ];

    foreach ($post_types as $type => $data) {
        $slug = get_locale() === 'vi' && isset($data['vi']) ? $data['vi'] : $data['default'];
        register_post_type($type, [
            'labels' => [
                'name' => __($data['label'], 'vmas-theme'),
                'singular_name' => __($data['label'], 'vmas-theme'),
            ],
            'public' => true,
            'has_archive' => true,
            'rewrite' => ['slug' => $slug],
            'supports' => ['title', 'editor', 'excerpt', 'thumbnail', 'page-attributes'],
            'menu_icon' => $data['icon'],
        ]);
    }

    // Đăng ký taxonomy cho project và album
    $taxonomies = [
        'project_category' => 'project',
        'album_category' => 'album',
        'team_category' => 'team',
    ];

    foreach ($taxonomies as $taxonomy => $post_type) {
        register_taxonomy($taxonomy, [$post_type], [
            'hierarchical' => true,
            'labels' => [
                'name' => _x('Categories', 'taxonomy general name', 'vmas-theme'),
                'singular_name' => _x('Category', 'taxonomy singular name', 'vmas-theme'),
            ],
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => ['slug' => $taxonomy],
        ]);
    }
}
add_action('init', 'vmas_register_custom_structures');

/**
 * Sửa chữ dưới footer của trang quản trị
 */
function tp_admin_footer_credits( $text ) {
    $text = '<strong>Trang quản trị VMAS</strong>';
    return $text;
}
add_filter( 'admin_footer_text', 'tp_admin_footer_credits' );

### Custom search
function searchfilter($query) {
    if ($query->is_search && !is_admin() ) {
        $query->set('post_type', array('post','artist','album'));
    }
    return $query;
}
    
add_filter('pre_get_posts','searchfilter');

// Thêm Logo
function vmas_customize_register($wp_customize) {
    // Thêm section cho logo
    $wp_customize->add_section('vmas_logo_section', array(
        'title'       => __('Logo', 'vmas-theme'),
        'priority'    => 30,
        'description' => 'Upload a logo for the site',
    ));

    // Thêm setting cho logo
    $wp_customize->add_setting('vmas_logo');

    // Thêm control cho logo
    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'vmas_logo', array(
        'label'    => __('Logo', 'vmas-theme'),
        'section'  => 'vmas_logo_section',
        'settings' => 'vmas_logo',
    )));
}
add_action('customize_register', 'vmas_customize_register');

// Thêm security headers
function vmas_security_setup() {
    // Remove WordPress version
    remove_action('wp_head', 'wp_generator');
    
    // Disable file editing
    if (!defined('DISALLOW_FILE_EDIT')) {
        define('DISALLOW_FILE_EDIT', true);
    }
}
add_action('init', 'vmas_security_setup');

// Helper function for about page sections
function vmas_get_about_sections() {
    return get_posts([
        'post_type' => 'about',
        'numberposts' => 10,
        'orderby' => 'menu_order',
        'order' => 'ASC',
        'meta_query' => [
            [
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            ]
        ]
    ]);
}

// Preload critical CSS for about page
function vmas_about_page_styles() {
    if (is_page_template('page-about-us.php')) {
        echo '<style>
            #about-us { background-color: var(--primary-color); }
            .py-80 { padding: 80px 0; }
            @media (max-width: 768px) { .py-80 { padding: 40px 0; } }
        </style>';
    }
}
add_action('wp_head', 'vmas_about_page_styles');

?>
