<?php
// Get all posts with post type "banner"
$args = array(
  'post_type' => 'banner',
  'orderby' => 'menu_order',
  'order' => 'desc',
);

$posts = get_posts( $args ); ?>

<div class="swiper hero-slider-one">
    <div class="swiper-wrapper">
        <?php foreach ( $posts as $k => $post ) {
            setup_postdata( $post );  
            $url = get_post_meta(get_the_ID(), '_url_link', true);
            $banner = get_post_meta(get_the_ID(), '_custom_image', true) ?? the_post_thumbnail_url(get_the_ID(), 'large');
        ?>
       
        <div class="home-one-slider position-relative swiper-slide">
           <a href="<?php echo $url; ?>">
           <?php if (wp_is_mobile()) : ?>
                <img loading="lazy" class="w-100" src="<?php the_post_thumbnail_url(get_the_ID(), 'large'); ?>" alt="<?php the_title(); ?>">
            <?php else : ?>
                <img loading="lazy" class="w-100" src="<?php echo $banner; ?>" alt="banner">
            <?php endif; ?>
            <div class="container position-absolute top-40 start-50 translate-middle">
                <div class="row justify-content-lg-between justify-content-center">
                    <div class="mx-auto col-xl-10 col-lg-6 col-md-9 col-sm-12 align-self-center">
                        <div class="content text-center text-lg-start">
                            <div class="banner-title" data-aos="fade-up" data-aos-ofset="100" data-aos-delay="1500">
                                <?php the_content();?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
           </a>
        </div>
        <?php } ?>
    </div>
</div>
