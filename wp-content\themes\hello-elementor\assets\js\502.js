"use strict";(self.webpackChunkhello_elementor=self.webpackChunkhello_elementor||[]).push([[502],{3502:function(e,C,l){l.r(C),l.d(C,{default:function(){return t}});var H=l(1609),n=l(3072),t=H.forwardRef(((e,C)=>H.createElement(n.A,{viewBox:"0 0 24 24",...e,ref:C},H.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.9341 3.93414C12.0125 2.8558 13.475 2.25 15 2.25H18C18.4142 2.25 18.75 2.58579 18.75 3V7C18.75 7.41421 18.4142 7.75 18 7.75H15C14.9337 7.75 14.8701 7.77634 14.8232 7.82322C14.7763 7.87011 14.75 7.9337 14.75 8V9.25H18C18.231 9.25 18.449 9.3564 18.5912 9.53844C18.7333 9.72048 18.7836 9.95785 18.7276 10.1819L17.7276 14.1819C17.6441 14.5158 17.3442 14.75 17 14.75H14.75V21C14.75 21.4142 14.4142 21.75 14 21.75H10C9.58579 21.75 9.25 21.4142 9.25 21V14.75H7C6.58579 14.75 6.25 14.4142 6.25 14V10C6.25 9.58579 6.58579 9.25 7 9.25H9.25V8C9.25 6.47501 9.8558 5.01247 10.9341 3.93414ZM15 3.75C13.8728 3.75 12.7918 4.19777 11.9948 4.9948C11.1978 5.79183 10.75 6.87283 10.75 8V10C10.75 10.4142 10.4142 10.75 10 10.75H7.75V13.25H10C10.4142 13.25 10.75 13.5858 10.75 14V20.25H13.25V14C13.25 13.5858 13.5858 13.25 14 13.25H16.4144L17.0394 10.75H14C13.5858 10.75 13.25 10.4142 13.25 10V8C13.25 7.53587 13.4344 7.09075 13.7626 6.76256C14.0908 6.43437 14.5359 6.25 15 6.25H17.25V3.75H15Z"}))))}}]);