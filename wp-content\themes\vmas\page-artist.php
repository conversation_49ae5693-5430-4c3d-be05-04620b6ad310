<?php
/*
Template Name: <PERSON><PERSON> sĩ
*/
get_header(); ?>

<?php
$args = array(
    'post_type' => 'artist',
    'posts_per_page' => '-1',
    'orderby'   => 'menu_order',
    'order'     => 'asc',
);

$query = new WP_Query($args);
?>
<div class="page-banner-area"></div>
<div class="page-banner-title py-5 bg-page">
    <div class="text-center mt-5">
        <h1 class="title-main">NGHỆ SĨ</h1>
        <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
    </div>
</div>
<div class="trending mt-5">
    <div class="swiper container">
        <div class="swiper-wrapper mx-auto row d-flex justify-content-center">
            <?php
            $k = 1;
            if ($query->have_posts()) :
                while ($query->have_posts()) : $query->the_post();
            ?>
                    <div class="col-lg-4 col-md-3 col-sm-6 mb-5">
                        <div class="movie-card-small position-relative swiper-slide" data-aos="fade-up" data-aos-offset="200">
                            <div class="thumb">
                                <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'w-100']); ?>
                            </div>
                            <a href="<?php the_permalink(); ?>" class="video-play-btn position-absolute">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="29" viewBox="0 0 24 29" fill="none">
                                    <path d="M22.2584 12.8002C23.5199 13.5823 23.5199 15.4177 22.2584 16.1998L3.05388 28.1066C1.72154 28.9326 6.40836e-07 27.9744 7.0936e-07 26.4068L1.75028e-06 2.59321C1.81881e-06 1.02557 1.72154 0.0673544 3.05388 0.893405L22.2584 12.8002Z" fill="currentColor" />
                                </svg>
                            </a>
                            <div class="details position-absolute text-center">
                                <h4 class="font-phudu fs-2 lh-sm"><a href="<?php the_permalink(); ?>" class="gradient-link fw-bold"><?php the_title(); ?></a></h4>
                            </div>
                        </div>
                    </div>
            <?php $k++;
                endwhile;
                wp_reset_postdata();
            else :
                echo '<p>No artists found.</p>';
            endif;
            ?>
        </div>
    </div>
</div>
<?php
get_footer();
?>
