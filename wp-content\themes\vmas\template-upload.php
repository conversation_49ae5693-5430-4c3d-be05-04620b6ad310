<?php
/* Template Name: Upload Tạo Ảnh Tùy Chỉnh */

get_header();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_image'])) {
    $fullname = sanitize_text_field($_POST['fullname']);
    $phone    = sanitize_text_field($_POST['phone']);
    $email    = sanitize_email($_POST['email']);
    $avatar   = $_FILES['avatar'];

    if ($avatar && $avatar['tmp_name']) {
        $upload_dir = wp_upload_dir();
        $template_path = get_template_directory() . '/template-bg.png';
        $font_path     = get_template_directory() . '/fonts/Roboto-Bold.ttf';

        // Kiểm tra file tồn tại
        if (!file_exists($template_path) || !file_exists($font_path)) {
            echo "<p style='color:red;'>Thiếu file nền hoặc font chữ.</p>";
            get_footer();
            exit;
        }

        // Load ảnh nền
        $template = imagecreatefrompng($template_path);

        // Load ảnh đại diện
        $user_avatar = imagecreatefromstring(file_get_contents($avatar['tmp_name']));
        $user_avatar = imagescale($user_avatar, 200, 200); // Resize

        // Ghép avatar vào template (tọa độ x: 50, y: 50)
        imagecopy($template, $user_avatar, 50, 50, 0, 0, imagesx($user_avatar), imagesy($user_avatar));

        // Ghi text thông tin
        $black = imagecolorallocate($template, 0, 0, 0);
        imagettftext($template, 20, 0, 270, 100, $black, $font_path, $fullname);
        imagettftext($template, 16, 0, 270, 140, $black, $font_path, $phone);
        imagettftext($template, 14, 0, 270, 180, $black, $font_path, $email);

        // Tạo ảnh tạm
        $filename = 'generated-' . time() . '.png';
        $filepath = $upload_dir['path'] . '/' . $filename;
        imagepng($template, $filepath);
        imagedestroy($template);

        $image_url = $upload_dir['url'] . '/' . $filename;

        echo "<p style='color:green;'>Tạo ảnh thành công!</p>";
        echo "<img src='$image_url' style='max-width:100%;'><br>";
        echo "<a href='$image_url' download class='button'>Tải ảnh về</a>";
    } else {
        echo "<p style='color:red;'>Vui lòng chọn ảnh đại diện!</p>";
    }
}
?>

<h2>Tạo ảnh cá nhân với template</h2>
<form method="POST" enctype="multipart/form-data">
    <label>Họ tên:</label><br>
    <input type="text" name="fullname" required><br><br>

    <label>Số điện thoại:</label><br>
    <input type="text" name="phone" required><br><br>

    <label>Email:</label><br>
    <input type="email" name="email" required><br><br>

    <label>Ảnh đại diện:</label><br>
    <input type="file" name="avatar" accept="image/*" required><br><br>

    <button type="submit" name="generate_image">Tạo ảnh</button>
</form>

<?php get_footer(); ?>
