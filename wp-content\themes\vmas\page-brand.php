<?php
/*
Template Name: Trang Nhãn hàng
*/
get_header(); ?>

<?php
$term_id = get_term_by('slug', 'nhan-hang', 'project_category')->term_id;
$args = array(
    'post_type' => 'project',
    'orderby'   => 'menu_order',
    'order'     => 'asc',
    'tax_query' => array(
        array(
        'taxonomy' => 'project_category',
        'field' => 'term_id',
        'terms' => $term_id
        )
    )
);
$query = new WP_Query($args);

$args = array(
    'post_slug' => 'nhan-hang',
    'post_type' => 'brand',
    'posts_per_page' => '-1',
    'order'     => 'asc'
);
$brand = new WP_Query($args);
?>
<div class="page-banner-area"></div>
<div class="page-banner-title py-5 bg-page">
    <div class="text-center mt-5">
        <h2 class="font-phudu">NHÃN HÀNG HỢP TÁC</h2>
        <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
    </div>
</div>
<!-- Start Trending Area -->
<div class="trending mt-5">
    <div class="swiper brand-logo-1 mb-3">
        <div class="swiper-wrapper">
        <?php
        if ( $brand->have_posts() ) : 
            while ( $brand->have_posts() ) : $brand->the_post();
                $gallery = get_post_gallery( get_the_ID(), false ); 
                if ( $gallery ) :
                    foreach ( $gallery['src'] as $k => $image ) :
                        if($k % 2 == 0) :
                        ?>
                        <div class="card card-brand swiper-slide">
                            <img loading="lazy" class="w-100 p-1" src="<?php echo esc_url( $image ); ?>" alt="logo-brand">
                        </div>
                        <?php
                        endif;
                    endforeach;
                endif;
            endwhile;
            wp_reset_postdata();
        endif; ?>
        </div>
    </div>
    <div class="swiper brand-logo-2">
        <div class="swiper-wrapper">
        <?php
        if ( $brand->have_posts() ) : 
            while ( $brand->have_posts() ) : $brand->the_post();
                $gallery = get_post_gallery( get_the_ID(), false ); 
                if ( $gallery ) :
                    foreach ( $gallery['src'] as $k => $image ) :
                        if($k % 2 == 1) :
                        ?>
                        <div class="card card-brand swiper-slide p-0">
                            <img loading="lazy" class="w-100 p-1" src="<?php echo esc_url( $image ); ?>" alt="logo-brand">
                        </div>
                        <?php
                        endif;
                    endforeach;
                endif;
            endwhile;
            wp_reset_postdata();
        endif; ?>
        </div>
    </div>
    <!-- Sliders -->
    <div class="container py-80">
        <div class="page-banner-title py-5">
            <div class="text-center">
                <h2 class="font-phudu">SẢN PHẨM HỢP TÁC</h2>
                <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
            </div>
        </div>
        <div class="brand row d-flex justify-content-center">
            <!-- Single Trending Movie Card -->
            <?php
            $k = 1;
            if ($query->have_posts()) :
                while ($query->have_posts()) : $query->the_post();
                
            ?>
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="movie-card-small position-relative swiper-slide" data-aos="fade-up" data-aos-offset="200">
                    <div class="thumb">
                        <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'thumb-img w-100']); ?>
                    </div>
                    <a href="#popup_<?php echo $k; ?>" class="video-play-btn popup_video position-absolute">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="29" viewBox="0 0 24 29" fill="none">
                            <path d="M22.2584 12.8002C23.5199 13.5823 23.5199 15.4177 22.2584 16.1998L3.05388 28.1066C1.72154 28.9326 6.40836e-07 27.9744 7.0936e-07 26.4068L1.75028e-06 2.59321C1.81881e-06 1.02557 1.72154 0.0673544 3.05388 0.893405L22.2584 12.8002Z" fill="currentColor"/>
                        </svg>
                    </a>
                    <div class="details position-absolute text-center">
                        <h4 class="font-phudu fs-2 lh-sm fw-bold"><span class="gradient-link"><?php the_title(); ?></spans></h4>
                    </div>
                </div>
                <div id="popup_<?php echo $k; ?>" class="mfp-hide">
                    <div class="mfp-iframe-scaler">
                        <?php the_content(); ?>
                    </div>
                </div>
            </div>
            <?php $k++; endwhile;
            // Reset post data
            wp_reset_postdata();
            endif;
            ?>
        </div>
    </div>
</div>
<!-- End Tranding Area -->
<?php
get_footer();
?>
