<?php
$term = get_term_by('slug', 'core-team', 'team_category');
$args = [
    'post_type' => 'team',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC',
    'tax_query' => [
        [
            'taxonomy' => 'team_category',
            'field' => 'term_id',
            'terms' => $term->term_id,
        ],
    ],
];
$teams = new WP_Query($args);
?>
<section class="teams" style="background: linear-gradient(1deg, #101010 10%, #9b1316 91%) !important;">
    <div class="container py-5">
        <h1 class="title-main pb-5"><?php _e($term->name, 'vms-theme'); ?></h1>
        <div class="team row d-flex flex-lg-row-reverse flex-md-row is-hover">
            <?php if ($teams->have_posts()) : ?>
                <?php while ($teams->have_posts()) : $teams->the_post(); ?>
                    <?php if ($teams->current_post === 0) : ?>
                        <div class="col-lg-5 col-md-6 col-sm-12">
                            <div class="card bg-transparent border-0" data-aos="fade-left">
                                <div class="thumb">
                                    <?php the_post_thumbnail('medium', ['loading' => 'lazy', 'class' => 'rounded w-100']); ?>
                                </div>
                                <div class="card-body text-center">
                                    <h3><?php the_title(); ?></h3>
                                    <?php the_excerpt(); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-7 col-md-12 col-sm-12 d-flex justify-content-center align-items-stretch" data-aos="fade-right">
                            <div class="row d-flex justify-content-center">
                    <?php else : ?>
                        <div class="col-lg-3 col-md-4 col-6">
                            <div class="card bg-transparent border-0">
                                <div class="thumb">
                                    <?php the_post_thumbnail('medium', ['loading' => 'lazy','class' => 'rounded w-100']); ?>
                                </div>
                                <div class="card-body text-center">
                                    <h3><?php the_title(); ?></h3>
                                    <?php the_excerpt(); ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endwhile; ?>
                <?php wp_reset_postdata(); ?>
            <?php endif; ?>
            </div>
        </div>
    </div>
</section>