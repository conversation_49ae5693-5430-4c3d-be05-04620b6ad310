<?php
$current_post = get_the_ID();
$term_id = get_term_by('slug', 'du-an', 'project_category')->term_id;
$args = array(
    'post_type' => 'project',
    'post_status' => 'publish',
    'post__not_in' => array($current_post),
    'posts_per_page' => -1,
    'order'     => 'asc',
    'tax_query' => array(
        array(
            'taxonomy' => 'project_category',
            'field' => 'term_id',
            'terms' => $term_id
        )
    )
);
$related = get_posts($args);
?>
<?php get_header(); ?>
<div class="page-banner-area"></div>
<div class="page-banner-title py-5 bg-page">
    <div class="text-center">
        <span class="fs-5 font-phudu">DỰ ÁN</span>
        <h1 class="title-main title-project py-3"><?php the_title(); ?></h1>
        <img class="shap1" src="<?php echo get_template_directory_uri() . '/assets/images/shap/title-white.png' ?>" alt="">
    </div>
</div>
<div class="trending">
    <div class="container">
        <?php
        while (have_posts()) : the_post(); ?>
            <div class="projects-main">
                <div class="content">
                    <?php the_content(); ?>
                </div>
                <div class="thumb">
                    <img loading="lazy" src="<?php the_post_thumbnail_url('full'); ?>" alt="<?php the_title(); ?>" class="card-img">
                </div>
            </div>
        <?php endwhile; ?>
    </div>
</div>
<?php if ($related) { ?>
    <section class="section-post-related py-80">
        <div class="container">
            <div class="row row-gap-4 d-flex justify-content-center">
                <div class="post-related my-3">
                    <h2><span>OUR PROJECTS</span></h2>
                </div>
                <?php
                foreach ($related as $k => $post) {
                    setup_postdata($post);
                ?>
                    <!-- Single Category Card -->
                    <div class="col-xl-3 col-lg-4 col-sm-6 mb-lg-4">
                        <div class="category-card p-3" data-aos="zoom-in" data-aos-offset="200">
                            <div class="thumbnail news-img">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium_large', ['loading' => 'lazy', 'class' => 'thumb-img w-100']); ?>
                                </a>
                            </div>
                            <div class="details">
                                <h3 class="fs-3 lh-sm mb-2"><?php the_title(); ?></h3>
                                <a href="<?php the_permalink(); ?>" class="hl-btn circle-btn flex-shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="18" viewBox="0 0 25 18" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.3368 0.763844V0H14.8091V0.763844C14.8091 3.66421 16.4166 6.45372 18.8361 8.10623H0V9.63392H18.8362C16.4166 11.2864 14.8091 14.0759 14.8091 16.9763V17.7401H16.3368V16.9763C16.3368 13.2214 19.6689 9.64694 23.6575 9.63396C23.6648 9.63398 23.672 9.63399 23.6793 9.63399H24.4431V9.63392V8.1063V8.10623H24.4425H23.6793H23.65C19.6648 8.0888 16.3368 4.51646 16.3368 0.763844Z" fill="currentColor" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php }
                wp_reset_postdata(); ?>
            </div>
        </div>
    </section>
<?php } ?>
<?php get_footer(); ?>
