<?php
$args = array(
    'post_type' => 'album',
    'order'     => 'asc'
);

$query = new WP_Query($args);
?>
<section class="music movie-slider swiper rotate-movie-slider py-80">
    <div class="container">
        <div class="d-sm-flex text-lg-start text-center align-items-center justify-content-sm-between">
            <div class="d-inline-flex align-item-center section-header">
                <h1 class="title-main pb-80">OUR PROJECTS</h1>
            </div>
        </div>
    </div>
    <div class="swiper-wrapper">
        <!-- One -->
        <?php
        if ($query->have_posts()) {
            $k = 1; 
            while ($query->have_posts()){
                $query->the_post(); 
                $k = $query->current_post + 1;
        ?>
        <div class="rotate-movie-card position-relative swiper-slide">
            <div class="thumb">
                <?php the_post_thumbnail('medium_large', ['loading' => 'lazy','class' => 'w-100']); ?>
            </div>
            <a href="<?php the_permalink(); ?>" class="video-play-btn position-absolute" data-effect="mfp-zoom-in">
                <svg xmlns="http://www.w3.org/2000/svg" width="37" height="43" viewBox="0 0 37 43" fill="none">
                    <path d="M35.1842 19.7904C36.467 20.5692 36.467 22.4308 35.1842 23.2096L3.97913 42.1555C2.64629 42.9647 0.941173 42.0052 0.941173 40.4459L0.941175 2.55405C0.941175 0.994777 2.64629 0.0352452 3.97913 0.844472L35.1842 19.7904Z" fill="white"/>
                </svg>
            </a>
            <div class="content position-absolute start-0 bottom-0 w-100 text-center">
                <h3 class="card-title text-uppercase lh-1"><a href="<?php the_permalink(); ?>" class="gradient-link"><?php the_title(); ?></a></h3>
                <div class="card-description p2"><?php the_excerpt(); ?></div>
            </div>
        </div>
        <?php }  
            $k++; } 
            wp_reset_postdata(); 
        ?>
    </div>
    <!-- Slider Button Wrapper -->
    <div class="slider-btn-wrapper d-flex align-item-center justify-content-center gap-4 mt-4 pt-lg-1">
        <button class="slider-btn slider-btn--big prev-slide" tabindex="0" aria-label="Previous slide">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="29" viewBox="0 0 40 29" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.91 1.5125V0.322754H15.2895V1.5125C15.2895 6.03007 12.7856 10.375 9.017 12.9489H38.3558V15.3284H9.01686C12.7856 17.9023 15.2895 22.2472 15.2895 26.7648V27.9545H12.91V26.7648C12.91 20.9162 7.71984 15.3487 1.50732 15.3285C1.496 15.3285 1.48468 15.3285 1.47336 15.3285H0.283611V15.3284V12.949V12.9489H0.284611H1.47336H1.519C7.72634 12.9217 12.91 7.35752 12.91 1.5125Z" fill="#fff"></path>
            </svg>
        </button>
        <button class="slider-btn slider-btn--big next-slide" tabindex="0" aria-label="Next slide">
            <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="90" height="90" class="linear-circle">
                <defs>
                    <linearGradient id="bg_gradient">
                        <stop offset="0%" stop-color="#5A0DFF"/>
                        <stop offset="39%" stop-color="#FF29B8"/>
                        <stop offset="100%" stop-color="#FF581C"/>
                    </linearGradient>
                </defs>
                <circle cx="24" cy="24" r="20" stroke-linecap="round"></circle>
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="29" viewBox="0 0 40 29" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26.4206 1.7022V0.512451H24.0411V1.7022C24.0411 6.21977 26.5449 10.5647 30.3136 13.1386H0.974731V15.5181H30.3137C26.545 18.092 24.0411 22.4369 24.0411 26.9545V28.1442H26.4206V26.9545C26.4206 21.1059 31.6107 15.5384 37.8232 15.5181C37.8346 15.5182 37.8459 15.5182 37.8572 15.5182H39.047V15.5181V13.1387V13.1386H39.046H37.8572H37.8116C31.6042 13.1114 26.4206 7.54722 26.4206 1.7022Z" fill="#fff"/>
            </svg>
        </button>
    </div>
</section>
<!-- End Movie Slider Area Two -->