<?php return array(
    'root' => array(
        'name' => 'elementor/hello-theme',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'c39d9f25619b465d7dd58f732f659675e5e801a2',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'elementor/hello-theme' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'c39d9f25619b465d7dd58f732f659675e5e801a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'elementor/wp-notifications-package' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'dd25ca9dd79402c3bb51fab112aa079702eb165e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elementor/wp-notifications-package',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
