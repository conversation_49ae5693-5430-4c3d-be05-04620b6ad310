<?php
/*
Template Name: Trang Giới thiệu
*/
get_header(); 

// Optimized query
$args = array(
    'post_type' => 'about',
    'posts_per_page' => 10,
    'orderby' => 'menu_order',
    'order' => 'ASC',
    'meta_query' => array(
        array(
            'key' => '_thumbnail_id',
            'compare' => 'EXISTS'
        )
    )
);

$query = new WP_Query($args);
?>

<main id="about-us" class="main">
    <?php if (has_post_thumbnail()) : ?>
        <div class="page-banner-area">
            <?php the_post_thumbnail('full', ['loading' => 'lazy', 'class' => 'w-100 h-auto']); ?>
        </div>
    <?php endif; ?>

    <?php
    if ($query->have_posts()) :
        $section_map = [
            'gioi-thieu' => 'intro',
            'su-menh' => 'mission',
            'muc-tieu' => 'goals',
            'cau-noi' => 'bridge',
            'co-so-vat-chat' => 'facilities',
            'nghe-si' => 'artists'
        ];

        while ($query->have_posts()) : 
            $query->the_post();
            $section_slug = $post->post_name;
            
            if (isset($section_map[$section_slug])) {
                get_template_part('template/about/section', $section_map[$section_slug]);
            }
        endwhile;
        wp_reset_postdata();
    else : ?>
        <section class="py-80">
            <div class="container text-center">
                <h2><?php _e('Nội dung đang được cập nhật', 'vmas-theme'); ?></h2>
            </div>
        </section>
    <?php endif; ?>
</main>

<?php 
get_template_part('template/home/<USER>');
get_footer();
?>
