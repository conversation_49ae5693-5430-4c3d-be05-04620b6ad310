<?php get_header(); ?>
<?php
while (have_posts()) : the_post();
    // $info = get_post_meta(get_the_ID(), '_artist_info', true);
    $product = get_post_meta(get_the_ID(), '_artist_products', true);
    $concert = get_post_meta(get_the_ID(), '_artist_concerts', true);
    $facebook_link = get_post_meta(get_the_ID(), '_facebook_link', true);
    $instagram_link = get_post_meta(get_the_ID(), '_instagram_link', true);
    $tiktok_link = get_post_meta(get_the_ID(), '_tiktok_link', true);
    $youtube_link = get_post_meta(get_the_ID(), '_youtube_link', true);
    $banner = get_post_meta(get_the_ID(), '_custom_image', true);
?>
<?php endwhile; ?>
<!-- Start Main -->
<div class="page-banner-area">
    <?php
    if (wp_is_mobile()) : ?>
        <?php the_post_thumbnail('medium', ['loading' => 'lazy', 'class' => 'w-100']); ?>
    <?php else : ?>
        <img loading="lazy" class="w-100" src="<?php echo $banner; ?>" alt="banner">
    <?php endif; ?>
    <!-- <span class="bg-text"><?php the_title(); ?></span> -->
    <div class="icon-bar">
        <ul class="social-media">
            <?php if ($facebook_link) { ?>
                <li class="social-media--item">
                    <a href="<?php echo esc_url($facebook_link); ?>" class="social-media--link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="8" height="16" viewBox="0 0 8 16" fill="none">
                            <path d="M5.33899 9.16479H7.21792L7.9695 6.20547H5.33899V4.72581C5.33899 3.96424 5.33899 3.24615 6.84214 3.24615H7.9695V0.760389C7.72471 0.728391 6.7993 0.656738 5.82217 0.656738C3.78203 0.656738 2.33269 1.88253 2.33269 4.13373V6.20547H0.0779705V9.16479H2.33269V15.4534H5.33899V9.16479Z" fill="currentColor"></path>
                        </svg>
                    </a>
                </li>
            <?php } ?>
            <?php if ($instagram_link) { ?>
                <li class="social-media--item">
                    <a href="<?php echo esc_url($instagram_link); ?>" class="social-media--link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8.59916 0.656738C9.43179 0.658114 9.85373 0.662524 10.2183 0.673378L10.3619 0.678068C10.5278 0.683965 10.6915 0.691364 10.8888 0.700612C11.6761 0.736991 12.2131 0.861531 12.6848 1.04465C13.1724 1.2327 13.5843 1.48671 13.9955 1.89795C14.4061 2.30919 14.6602 2.72227 14.8489 3.20873C15.0313 3.67977 15.1559 4.21741 15.1929 5.00473C15.2017 5.20203 15.2088 5.36569 15.2147 5.53159L15.2193 5.67519C15.2301 6.03975 15.2351 6.46176 15.2366 7.29442L15.2372 7.84606C15.2373 7.91346 15.2373 7.98301 15.2373 8.05477L15.2372 8.26349L15.2368 8.8152C15.2354 9.64783 15.231 10.0699 15.2201 10.4344L15.2154 10.578C15.2095 10.7439 15.2022 10.9076 15.1929 11.1048C15.1565 11.8922 15.0313 12.4292 14.8489 12.9008C14.6608 13.3886 14.4061 13.8004 13.9955 14.2116C13.5843 14.6223 13.1706 14.8763 12.6848 15.0649C12.2131 15.2474 11.6761 15.372 10.8888 15.409C10.6915 15.4178 10.5278 15.4249 10.3619 15.4307L10.2183 15.4354C9.85373 15.4462 9.43179 15.4511 8.59916 15.4528L8.04745 15.4534C7.98005 15.4534 7.9105 15.4534 7.83874 15.4534H7.63002L7.07831 15.4528C6.24568 15.4515 5.82368 15.4471 5.45912 15.4362L5.31552 15.4315C5.14961 15.4256 4.98596 15.4182 4.78867 15.409C4.00133 15.3726 3.46494 15.2474 2.99267 15.0649C2.50559 14.8769 2.09312 14.6223 1.68189 14.2116C1.27065 13.8004 1.01725 13.3867 0.828588 12.9008C0.645474 12.4292 0.521548 11.8922 0.484555 11.1048C0.475766 10.9076 0.468596 10.7439 0.462788 10.578L0.458135 10.4344C0.447311 10.0699 0.442376 9.64783 0.440778 8.8152L0.440681 7.29442C0.442058 6.46176 0.44646 6.03975 0.457313 5.67519L0.462012 5.53159C0.467908 5.36569 0.475307 5.20203 0.484555 5.00473C0.520926 4.21679 0.645474 3.68039 0.828588 3.20873C1.01663 2.72166 1.27065 2.30919 1.68189 1.89795C2.09312 1.48671 2.50621 1.23331 2.99267 1.04465C3.46432 0.861531 4.00072 0.737605 4.78867 0.700612C4.98596 0.69183 5.14961 0.684661 5.31552 0.678853L5.45912 0.674199C5.82368 0.663367 6.24568 0.658433 7.07831 0.656834L8.59916 0.656738ZM7.83874 4.35551C5.79457 4.35551 4.13944 6.01244 4.13944 8.05477C4.13944 10.0989 5.79637 11.7541 7.83874 11.7541C9.88288 11.7541 11.538 10.0972 11.538 8.05477C11.538 6.01064 9.88103 4.35551 7.83874 4.35551ZM7.83874 5.83522C9.0646 5.83522 10.0583 6.82861 10.0583 8.05477C10.0583 9.28064 9.0649 10.2743 7.83874 10.2743C6.61287 10.2743 5.61915 9.28101 5.61915 8.05477C5.61915 6.8289 6.6125 5.83522 7.83874 5.83522ZM11.723 3.24572C11.213 3.24572 10.7982 3.65998 10.7982 4.16991C10.7982 4.67986 11.2124 5.09475 11.723 5.09475C12.2329 5.09475 12.6478 4.68051 12.6478 4.16991C12.6478 3.65998 12.2322 3.24509 11.723 3.24572Z" fill="currentColor"></path>
                        </svg>
                    </a>
                </li>
            <?php } ?>
            <?php if ($tiktok_link) { ?>
                <li class="social-media--item">
                    <a href="<?php echo esc_url($tiktok_link); ?>" class="social-media--link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="16" viewBox="0 0 14 16" fill="none">
                            <path d="M10.0452 5.27725V10.6445C10.0452 13.3004 7.81249 15.4534 5.0582 15.4534C2.30396 15.4534 0.0711975 13.3004 0.0711975 10.6445C0.0711975 7.98853 2.30396 5.83555 5.0582 5.83555C5.45433 5.83555 5.83963 5.88008 6.20905 5.96423V8.30422C5.86027 8.14442 5.47005 8.05504 5.0582 8.05504C3.57514 8.05504 2.37289 9.21436 2.37289 10.6445C2.37289 12.0745 3.57514 13.2339 5.0582 13.2339C6.54126 13.2339 7.74351 12.0745 7.74351 10.6445V0.656738H10.0452C10.0452 2.69972 11.7627 4.35589 13.8814 4.35589V6.57538C12.4298 6.57538 11.096 6.08952 10.0452 5.27725Z" fill="currentColor"></path>
                        </svg>
                    </a>
                </li>
            <?php } ?>
            <?php if ($youtube_link) { ?>
                <li class="social-media--item">
                    <a href="<?php echo esc_url($youtube_link); ?>" class="social-media--link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="16" viewBox="0 0 19 16" fill="none">
                            <path d="M9.51528 0.656738C9.98947 0.659457 11.1759 0.671406 12.4364 0.724002L12.8834 0.744302C14.1525 0.806895 15.4206 0.913825 16.0496 1.0965C16.8885 1.342 17.5479 2.05833 17.7707 2.96636C18.1256 4.40827 18.17 7.22255 18.1755 7.9036L18.1763 8.04473V8.05463C18.1763 8.05463 18.1763 8.05805 18.1763 8.06462L18.1755 8.20575C18.17 8.8868 18.1256 11.7011 17.7707 13.143C17.5448 14.0543 16.8854 14.7707 16.0496 15.0128C15.4206 15.1955 14.1525 15.3024 12.8834 15.365L12.4364 15.3853C11.1759 15.4379 9.98947 15.4498 9.51528 15.4526L9.30717 15.4534H9.29793C9.29793 15.4534 9.29483 15.4534 9.2887 15.4534L9.08077 15.4526C8.07716 15.4469 3.8809 15.3996 2.5463 15.0128C1.70737 14.7673 1.04799 14.051 0.825102 13.143C0.470261 11.7011 0.425904 8.8868 0.420364 8.20575V7.9036C0.425904 7.22255 0.470261 4.40827 0.825102 2.96636C1.05108 2.05497 1.71046 1.33864 2.5463 1.0965C3.8809 0.709667 8.07716 0.662491 9.08077 0.656738H9.51528ZM7.52227 4.81772V11.2916L12.8493 8.05463L7.52227 4.81772Z" fill="currentColor"></path>
                        </svg>
                    </a>
                </li>
            <?php } ?>
        </ul>
    </div>
</div>
<main class="main aritst">
    <div class="page-banner-title pt-5">
        <div class="text-center">
            <span class="fs-2 font-phudu">NGHỆ SĨ</span>
            <h1 class="title-main py-4"><?php the_title(); ?></h1>
        </div>
    </div>
    <div class="container">
        <div class="row d-flex justify-content-center">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                    <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab" aria-controls="home" aria-selected="true">
                        <?php _e('Giới thiệu', 'vmas-theme'); ?>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="false">
                        <?php _e('Sản phẩm', 'vmas-theme'); ?>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                        <?php _e('Thành tích', 'vmas-theme'); ?>
                    </button>
                </li>
            </ul>
            <div class="tab-content w-100 col-10" id="myTabContent">
                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                    <div class="main-body">
                        <?php
                        global $post;
                        $term = get_term_by('slug', $post->post_name, 'team_category');
                        $args = [
                            'post_type' => 'team',
                            'orderby' => 'menu_order',
                            'order' => 'ASC',
                            'posts_per_page' => -1,
                            'tax_query' => [
                                [
                                    'taxonomy' => 'team_category',
                                    'field' => 'term_id',
                                    'terms' => $term->term_id,
                                ],
                            ],
                        ];
                        $teams = new WP_Query($args); ?>
                        <?php if ($teams->have_posts()) : ?>
                            <div class="text-left post-related">
                                <h2><span>Thành viên</span></h2>
                            </div>
                            <div class="team row d-flex justify-content-center pt-3 pb-5">
                                <?php while ($teams->have_posts()) : $teams->the_post(); ?>
                                    <div class="col-lg-4 col-md-6 col-6 mb-3">
                                        <div class="card bg-transparent border-0" data-aos="fade-left">
                                            <div class="thumb">
                                                <?php the_post_thumbnail('medium', ['loading' => 'lazy', 'class' => 'rounded w-100']); ?>
                                            </div>
                                            <div class="card-body text-center mt-2">
                                                <h3><?php the_title(); ?></h3>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                                <?php wp_reset_postdata(); ?>
                            </div>
                        <?php endif; ?>
                        <?php the_content(); ?>
                    </div>
                </div>
                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="container">
                        <div class="text-left post-related">
                            <h2><span>Sản phẩm âm nhạc</span></h2>
                        </div>
                        <ul class="category-list">
                            <?php
                            $term_id = get_term_by('slug', $post->post_name, 'album_category')->term_id;
                            $args = array(
                                'post_type' => 'album',
                                'posts_per_page' => -1,
                                'orderby' => 'menu_order',
                                'order' => 'ASC',
                                'tax_query' => array(
                                    array(
                                        'taxonomy' => 'album_category',
                                        'field' => 'term_id',
                                        'terms' => $term_id
                                    )
                                )
                            );
                            $query = new WP_Query($args);
                            if ($query->have_posts()) :
                                while ($query->have_posts()) : $query->the_post();
                            ?>

                                    <li class="category-list--item row">
                                        <div class="col-lg-5 mb-4 mb-lg-0">
                                            <div class="thumb me-lg-3">
                                                <?php the_post_thumbnail('medium', ['loading' => 'lazy', 'class' => 'w-100']); ?>
                                            </div>
                                        </div>
                                        <div class="col-lg-7">
                                            <div class="content">
                                                <h3 class="title mb-2">
                                                    <a href="<?php the_permalink(); ?>" class="gradient-link"><?php the_title(); ?></a>
                                                </h3>
                                                <ul class="text-uppercase color-paragraph">
                                                    <li class="d-inline-block"><?php the_excerpt(); ?></li>
                                                </ul>
                                                <div class="excerpt mb-3">
                                                    <?php the_content(); ?>
                                                </div>
                                                <a href="<?php the_permalink(); ?>" class="hl-btn btn-base small-btn fs-18 fw-normal text-uppercase flex-shrink-0 radius-100">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="15" viewBox="0 0 13 15" fill="none">
                                                        <path d="M1 2.66967C1 1.90998 1 1.53013 1.16686 1.31807C1.31227 1.13326 1.53485 1.01765 1.77687 1.00119C2.05461 0.982329 2.39036 1.18772 3.06203 1.59855L10.9592 6.42893C11.542 6.78532 11.8335 6.96359 11.934 7.19025C12.022 7.38826 12.022 7.61173 11.934 7.80974C11.8335 8.0364 11.542 8.21467 10.9592 8.57106L3.06203 13.4014C2.39036 13.8123 2.05461 14.0177 1.77687 13.9988C1.53485 13.9824 1.31227 13.8667 1.16686 13.6819C1 13.4699 1 13.0901 1 12.3304V2.66967Z" fill="currentColor" fill-opacity="0.2" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                    <span class="pt-0">Xem chi tiết</span>
                                                </a>
                                            </div>
                                        </div>
                                    </li>
                                <?php
                                endwhile; ?>
                                <?php wp_reset_postdata(); ?>
                            <?php else : ?>
                                <h3 class="fs-3 fw-bold"><?php _e('COMING SOON...', 'vmas-theme'); ?></h3>
                            <?php endif; ?>
                        </ul>
                        <?php
                        if ($query->have_posts()) :
                            while ($query->have_posts()) : $query->the_post();
                                $youtube_link = get_post_meta($post->ID, '_youtube_link', true);
                                $spotify_link = get_post_meta($post->ID, '_spotify_link', true);
                        ?>
                                <div class="content">
                                    <?php if ($youtube_link) : ?>
                                        <div class="text-left post-related py-3">
                                            <h2><span>Music Video</span></h2>
                                        </div>
                                    <?php
                                        echo $youtube_link;
                                    endif;
                                    ?>
                                </div>
                            <?php
                            endwhile; ?>
                            <?php wp_reset_postdata(); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                    <article class="main-body">
                        <?php echo do_shortcode($concert); ?>
                    </article>
                </div>
            </div>
        </div>
    </div>
</main>
<!-- End Main -->
<?php get_footer(); ?>
